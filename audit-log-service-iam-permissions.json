{"Version": "2012-10-17", "Statement": [{"Sid": "DynamoDBPermissions", "Effect": "Allow", "Action": ["dynamodb:PutItem", "dynamodb:GetItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:CreateTable", "dynamodb:ListTables"], "Resource": ["arn:aws:dynamodb:eu-west-2:557348263238:table/production_audit_logs", "arn:aws:dynamodb:eu-west-2:557348263238:table/production_audit_logs/*"]}, {"Sid": "S3PermissionsForArchiveBucket", "Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket", "s3:GetBucketLocation"], "Resource": ["arn:aws:s3:::your-archive-bucket-name", "arn:aws:s3:::your-archive-bucket-name/*"]}, {"Sid": "SecretsManagerPermissions", "Effect": "Allow", "Action": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>"], "Resource": ["arn:aws:secretsmanager:eu-west-2:557348263238:secret:audit-log-service/production/env-list-Wixirg"]}, {"Sid": "CloudWatchLogsPermissions", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents", "logs:DescribeLogGroups", "logs:DescribeLogStreams"], "Resource": ["arn:aws:logs:eu-west-2:557348263238:log-group:inndex-prod-audit-log-service", "arn:aws:logs:eu-west-2:557348263238:log-group:inndex-prod-audit-log-service:*"]}]}